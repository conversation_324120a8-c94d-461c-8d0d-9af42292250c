<template>
  <div class="tratamento-content">
    <!-- Abas para alternar entre Fotos e Radiografias e Modelos 3D -->
    <!-- TEMPORARIAMENTE COMENTADO - Reativar quando modelos 3D estiver 100% funcional -->
    <!--
    <div class="nav-wrapper position-relative end-0 mb-3">
      <ul class="nav nav-pills nav-fill p-1 pb-0 mx-auto" role="tablist">
        <li class="nav-item">
          <a class="nav-link mb-0 px-0 py-2 d-flex align-items-center justify-content-center"
            :class="{ 'active': activeTab === 'fotosRadiografias' }" @click="selectTab('fotosRadiografias')" href="javascript:;"
            role="tab" :aria-selected="activeTab === 'fotosRadiografias' ? 'true' : 'false'">
            <font-awesome-icon :icon="['fas', 'image']" class="me-2" style="font-size: 0.8rem;" />
            <span>Fotos e radiografias</span>
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link mb-0 px-0 py-2 d-flex align-items-center justify-content-center"
            :class="{ 'active': activeTab === 'modelos3d' }" @click="selectTab('modelos3d')" href="javascript:;"
            role="tab" :aria-selected="activeTab === 'modelos3d' ? 'true' : 'false'">
            <font-awesome-icon :icon="['fas', 'cube']" class="me-2" style="font-size: 0.8rem;" />
            <span>Modelos 3D</span>
          </a>
        </li>
      </ul>
    </div>
    -->

    <!-- Conteúdo da aba Fotos e radiografias -->
    <Transition>
      <div v-show="activeTab === 'fotosRadiografias'">
        <!-- Modo de diagnóstico - slots de imagens -->
        <div v-if="mode === 'diagnostic'" class="diagnostic-mode">
          <!-- Balão flutuante para aviso de documentação inicial -->
          <Transition>
            <div v-if="shouldShowDiagnosticInfo" class="diagnostic-info-floating mb-4">
              <div class="info-message-floating">
                <div class="d-flex align-items-center justify-content-between">
                  <div class="d-flex align-items-center">
                    <font-awesome-icon :icon="['fas', 'info-circle']" class="info-icon me-2" />
                    <span>Anexe a documentação inicial do paciente nos slots abaixo. Estas imagens serão utilizadas para o diagnóstico e planejamento do tratamento.</span>
                  </div>
                  <button @click="closeDiagnosticInfo" class="close-info-btn" title="Fechar">
                    <font-awesome-icon :icon="['fas', 'times']" />
                  </button>
                </div>
              </div>
            </div>
          </Transition>

          <!-- Container único v-viewer para todas as imagens de diagnóstico -->
          <div v-viewer class="diagnostic-viewer-container">
            <!-- Slots para imagens extra-bucais -->
            <div class="diagnostic-group mb-4" :class="{ 'mt-3': !shouldShowDiagnosticInfo }">
              <div class="diagnostic-group-header">
                <div class="group-icon-wrapper">
                  <div class="group-icon">
                    <font-awesome-icon :icon="['fas', 'user-circle']" />
                  </div>
                </div>
                <h5 class="diagnostic-group-title">Extra-bucais</h5>
                <div class="group-description">Fotografias faciais e do sorriso</div>
              </div>
              <div class="diagnostic-slots-container extra-bucais-grid">
                <div
                  v-for="slot in diagnosticSlots.extraBucais"
                  :key="slot.id"
                  class="diagnostic-slot modern-slot"
                  :class="{ 'has-image': slot.preview, 'slot-active': activeSlot === slot }"
                  @click="handleSlotClick(slot)"
                  @dragover.prevent="onDragOverSlot(slot)"
                  @dragleave.prevent="onDragLeaveSlot()"
                  @drop.prevent="onDropSlot($event, slot)"
                >
                  <template v-if="!slot.preview">
                    <div class="slot-empty modern-empty">
                      <div class="empty-icon-wrapper">
                        <font-awesome-icon :icon="['fas', 'camera']" class="slot-icon" />
                        <div class="upload-indicator">
                          <font-awesome-icon :icon="['fas', 'plus']" class="plus-icon" />
                        </div>
                      </div>
                      <div class="slot-label">{{ slot.label }}</div>
                      <div class="slot-hint">Clique ou arraste</div>
                    </div>
                  </template>
                  <template v-else>
                    <div class="slot-preview modern-preview">
                      <img :src="slot.preview" :alt="slot.label" />
                      <button class="slot-edit-btn-modern" @click.stop="editDiagnosticSlot(slot)" title="Editar imagem">
                        <font-awesome-icon :icon="['fas', 'edit']" />
                      </button>
                    </div>
                    <div class="slot-label-card modern-label">{{ slot.label }}</div>
                  </template>
                </div>
              </div>
            </div>

            <!-- Slots para imagens intra-bucais -->
            <div class="diagnostic-group mb-4">
              <div class="diagnostic-group-header">
                <div class="group-icon-wrapper">
                  <div class="group-icon">
                    <font-awesome-icon :icon="['fas', 'teeth']" />
                  </div>
                </div>
                <h5 class="diagnostic-group-title">Intra-bucais</h5>
                <div class="group-description">Fotografias intraorais específicas</div>
              </div>
              <div class="diagnostic-slots-container intra-bucais-layout">
                <!-- Primeira linha: Lateral direita, Frontal, Lateral esquerda -->
                <div class="intra-bucais-row-1">
                  <div
                    v-for="slot in intraBucaisRow1"
                    :key="slot.id"
                    class="diagnostic-slot modern-slot intra-slot"
                    :class="{ 'has-image': slot.preview, 'slot-active': activeSlot === slot }"
                    @click="handleSlotClick(slot)"
                    @dragover.prevent="onDragOverSlot(slot)"
                    @dragleave.prevent="onDragLeaveSlot()"
                    @drop.prevent="onDropSlot($event, slot)"
                  >
                    <template v-if="!slot.preview">
                      <div class="slot-empty modern-empty">
                        <div class="empty-icon-wrapper">
                          <font-awesome-icon :icon="['fas', 'camera']" class="slot-icon" />
                          <div class="upload-indicator">
                            <font-awesome-icon :icon="['fas', 'plus']" class="plus-icon" />
                          </div>
                        </div>
                        <div class="slot-label">{{ slot.label }}</div>
                        <div class="slot-hint">Clique ou arraste</div>
                      </div>
                    </template>
                    <template v-else>
                      <div class="slot-preview modern-preview">
                        <img :src="slot.preview" :alt="slot.label" />
                        <button class="slot-edit-btn-modern" @click.stop="editDiagnosticSlot(slot)" title="Editar imagem">
                          <font-awesome-icon :icon="['fas', 'edit']" />
                        </button>
                      </div>
                      <div class="slot-label-card modern-label">{{ slot.label }}</div>
                    </template>
                  </div>
                </div>

                <!-- Segunda linha: Oclusal superior, Oclusal inferior -->
                <div class="intra-bucais-row-2">
                  <div
                    v-for="slot in intraBucaisRow2"
                    :key="slot.id"
                    class="diagnostic-slot modern-slot intra-slot"
                    :class="{ 'has-image': slot.preview, 'slot-active': activeSlot === slot }"
                    @click="handleSlotClick(slot)"
                    @dragover.prevent="onDragOverSlot(slot)"
                    @dragleave.prevent="onDragLeaveSlot()"
                    @drop.prevent="onDropSlot($event, slot)"
                  >
                    <template v-if="!slot.preview">
                      <div class="slot-empty modern-empty">
                        <div class="empty-icon-wrapper">
                          <font-awesome-icon :icon="['fas', 'camera']" class="slot-icon" />
                          <div class="upload-indicator">
                            <font-awesome-icon :icon="['fas', 'plus']" class="plus-icon" />
                          </div>
                        </div>
                        <div class="slot-label">{{ slot.label }}</div>
                        <div class="slot-hint">Clique ou arraste</div>
                      </div>
                    </template>
                    <template v-else>
                      <div class="slot-preview modern-preview">
                        <img :src="slot.preview" :alt="slot.label" />
                        <button class="slot-edit-btn-modern" @click.stop="editDiagnosticSlot(slot)" title="Editar imagem">
                          <font-awesome-icon :icon="['fas', 'edit']" />
                        </button>
                      </div>
                      <div class="slot-label-card modern-label">{{ slot.label }}</div>
                    </template>
                  </div>
                </div>
              </div>
            </div>

            <!-- Slots para radiografias -->
            <div class="diagnostic-group mb-4">
              <div class="diagnostic-group-header">
                <div class="group-icon-wrapper">
                  <div class="group-icon">
                    <font-awesome-icon :icon="['fas', 'x-ray']" />
                  </div>
                </div>
                <h5 class="diagnostic-group-title">Radiografias</h5>
                <div class="group-description">Exames radiográficos complementares</div>
              </div>
              <div class="diagnostic-slots-container radiografias-grid">
                <div
                  v-for="slot in diagnosticSlots.radiografias"
                  :key="slot.id"
                  class="diagnostic-slot modern-slot radio-slot"
                  :class="{ 'has-image': slot.preview, 'slot-active': activeSlot === slot }"
                  @click="handleSlotClick(slot)"
                  @dragover.prevent="onDragOverSlot(slot)"
                  @dragleave.prevent="onDragLeaveSlot()"
                  @drop.prevent="onDropSlot($event, slot)"
                >
                  <template v-if="!slot.preview">
                    <div class="slot-empty modern-empty">
                      <div class="empty-icon-wrapper">
                        <font-awesome-icon :icon="['fas', 'x-ray']" class="slot-icon" />
                        <div class="upload-indicator">
                          <font-awesome-icon :icon="['fas', 'plus']" class="plus-icon" />
                        </div>
                      </div>
                      <div class="slot-label">{{ slot.label }}</div>
                      <div class="slot-hint">Clique ou arraste</div>
                    </div>
                  </template>
                  <template v-else>
                    <div class="slot-preview modern-preview">
                      <img :src="slot.preview" :alt="slot.label" />
                      <button class="slot-edit-btn-modern" @click.stop="editDiagnosticSlot(slot)" title="Editar imagem">
                        <font-awesome-icon :icon="['fas', 'edit']" />
                      </button>
                    </div>
                    <div class="slot-label-card modern-label">{{ slot.label }}</div>
                  </template>
                </div>
              </div>
            </div>
          </div>

          <!-- Input oculto para upload de imagens de diagnóstico -->
          <input
            id="diagnosticImageInput"
            type="file"
            accept="image/*"
            @change="handleDiagnosticImageSelect"
            hidden
          />
        </div>

        <!-- Modo regular - visualização de imagens por data -->
        <div v-else class="row border-between">
          <div class="col-12">
            <!-- Área expandida de drag'n'drop que inclui header e lista -->
            <div class="drag-drop-area"
              :class="{ 'drag-over': dragOverContainer }"
              @dragover.prevent="onDragOverContainer"
              @dragleave.prevent="onDragLeaveContainer"
              @drop.prevent="onDropContainer"
            >
              <!-- Header com botão e texto informativo -->
              <div class="images-header mb-4">
                <div class="drag-drop-info">
                  <div class="drag-option">
                    <font-awesome-icon :icon="['fas', 'hand-paper']" class="me-2" />
                    <span>Arraste imagens aqui</span>
                  </div>
                  <div class="divider-text">ou</div>
                  <div class="click-option">
                    <font-awesome-icon :icon="['fas', 'mouse-pointer']" class="me-2" />
                    <span>Clique no botão "<span class="text-muted" style="text-transform: uppercase; font-size: 9pt;">enviar imagens</span>"</span>
                  </div>
                </div>
                <div class="header-actions">
                  <button
                    class="btn btn-primary btn-sm"
                    @click="chooseImageFile()"
                    title="Enviar novas imagens"
                  >
                    <font-awesome-icon :icon="['fas', 'upload']" class="me-2" />
                    Enviar imagens
                  </button>
                </div>
              </div>

              <!-- Container das imagens -->
              <div class="image-group-container">

              <!-- Mensagem quando não há imagens -->
              <div v-if="groupedImagesByDate.length === 0" class="no-images-message">
                <div class="message-content">
                  <font-awesome-icon :icon="['fas', 'images']" class="main-icon mb-3" size="2x" />
                  <p class="main-text mb-0">
                    Aqui você pode armazenar as imagens realizadas durante o tratamento do paciente.
                  </p>
                </div>
              </div>
              <div v-for="group in groupedImagesByDate" :key="group.date" class="date-group">
                <div class="date-group-header">
                  <div class="date-group-header-content">
                    <span class="date-text">{{ $filters.dateLong(group.date) }}</span>
                  </div>
                  <div class="date-group-actions">
                    <span class="date-how-much">{{ $filters.howMuchTime(group.date, { type: 'date' }) }}</span>
                  </div>
                </div>
                <div
                  class="images-container w-100"
                  v-viewer
                >
                  <div v-for="image in group.images" :key="image.url">
                    <img :src="image.url"
                      :alt="getImageDescription(image)"
                      :title="getImageDescription(image)" />
                  </div>
                </div>
              </div>

            </div>
            </div>

            <!-- Input oculto para upload de imagens -->
            <input
              id="imageFileInput"
              type="file"
              accept="image/*"
              multiple
              @change="setImagePreviews"
              hidden
            />
            <Transition>
              <div v-if="pendingImagePreviews.length > 0" class="row mt-3">
                <div class="col-12 d-flex flex-wrap justify-content-center gap-3">
                  <div v-for="(preview, index) in pendingImagePreviews" :key="index" class="upload-preview">
                    <div class="image-preview-container">
                      <img :src="preview" class="image-preview" />
                      <button
                        type="button"
                        class="btn-cancel-image"
                        @click="removeImagePreview(index)"
                        title="Cancelar upload desta imagem"
                      >
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                    <input type="date" class="text-center" v-model="pendingImageMetadata[index].date" />
                    <input type="text" class="text-center" v-model="pendingImageMetadata[index].description" placeholder="Descrição" />
                  </div>
                </div>
                <div class="col-12 text-center mt-3">
                  <input
                    id="imageFileInputAdditional"
                    type="file"
                    accept="image/*"
                    multiple
                    @change="setImagePreviews"
                    hidden
                  />
                  <button
                    class="btn btn-sm bg-gradient-primary me-2 mb-2"
                    @click="chooseImageFile('imageFileInputAdditional')"
                  >
                    <i class="fas fa-upload me-2" style="font-size: 1.1rem;"></i>
                    Enviar Mais Imagens
                  </button>
                </div>
                <div class="col-12 text-center mt-2">
                  <button
                    class="btn btn-sm btn-danger me-2"
                    @click="cancelImageUpload"
                  >
                    <i class="fas fa-times me-2" style="font-size: 1.1rem;"></i>
                    Cancelar
                  </button>
                  <button
                    class="btn btn-sm btn-primary"
                    @click="confirmImageUpload"
                  >
                    <i class="fas fa-save me-2" style="font-size: 1.1rem;"></i>
                    Salvar
                  </button>
                </div>
              </div>
            </Transition>
          </div>
        </div>
      </div>
    </Transition>

    <!-- Conteúdo da aba Modelos 3D - carregado sob demanda -->
    <Transition>
      <div v-show="activeTab === 'modelos3d'">
        <Modelos3D
          v-if="modelosLoaded"
          :paciente="paciente"
          @pacienteChange="$emit('pacienteChange')"
        />
        <div v-else class="modelos-loading-container">
          <div class="modelos-loading-spinner">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Carregando...</span>
            </div>
          </div>
          <div class="modelos-loading-text">Carregando visualizador 3D...</div>
        </div>
      </div>
    </Transition>

    <Transition>
      <div class="row" v-show="!hideAnalyseButton && activeTab === 'fotosRadiografias'">
        <div class="col-12">
          <div class="next-btn-container py-2 py-md-3">
            <button
              class="btn btn-success mb-0"
              @click="iniciarAnalise"
              :disabled="safePatientImages.length === 0"
            >
              <i class="me-2 fas fa-search" style="font-size: 13pt"></i>
              <span style="font-size: 10pt"> INICIAR ANÁLISE </span>
              <i class="ms-2 fas fa-chevron-right" style="font-size: 13pt"></i>
            </button>
          </div>
        </div>
      </div>
    </Transition>

    <!-- Botão flutuante para upload (apenas em telas móveis) -->
    <div class="fab-container d-md-none">
      <button
        class="btn btn-primary btn-fab"
        @click="activeTab === 'fotosRadiografias' ? chooseImageFile() : openModeloFileInput()"
        :title="activeTab === 'fotosRadiografias' ? 'Enviar Imagens' : 'Enviar Modelos 3D'"
      >
        <i class="fas fa-upload"></i>
      </button>
    </div>
  </div>
</template>

<style scoped>
.images-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  padding: 15px 10px;
  background: #f8f9fa;
  border-radius: 0 0 8px 8px;
  gap: 12px;
  min-height: 125px;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
}

.images-container > div {
  background: #000;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
}

.images-container img {
  width: 160px;
  height: auto;
  aspect-ratio: 9/6;
  object-fit: cover;
  filter: brightness(95%);
  transition: all 0.3s ease;
}

.images-container > div:hover {
  transform: translateY(-5px) scale(1.03);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.images-container > div:hover img {
  filter: brightness(105%);
}

.images-container > div:hover::after {
  opacity: 0.3;
}

/* Botão de excluir removido - será implementado em outro local futuramente */

.empty-state-cell {
  border-radius: 3px;
  padding: 10px 20px;
  font-style: italic;
}

.image-preview {
  border: 2px solid #aaa;
  border-radius: 5px;
  width: 160px !important;
  height: 160px !important;
  object-fit: cover;
}

.upload-preview {
  padding: 10px;
  padding-bottom: 0px;
  border: 1px dashed #ccc;
  border-radius: 5px;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: 180px;
}

.image-preview-container {
  position: relative;
  width: 100%;
}

.upload-preview img.image-preview {
  width: 100%;
  height: auto;
  border-radius: 5px 5px 0 0;
  object-fit: cover;
}

.btn-cancel-image {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #dc3545;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 12px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  z-index: 10;
}

.btn-cancel-image:hover {
  background-color: #c82333;
  transform: scale(1.1);
}

.upload-preview label {
  font-weight: 600;
  margin-bottom: 4px;
  display: block;
}

.upload-preview input[type="date"],
.upload-preview input[type="text"] {
  width: 100%;
  box-sizing: border-box;
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 0.9rem;
  background: #FFF;
}

.section-header {
  padding-bottom: 10px;
}

.date-group-header {
  font-weight: 500;
  font-size: 0.9rem;
  padding: 8px 15px;
  color: #495057;
  background-color: #e9ecef;
  text-align: left;
  border-radius: 8px 8px 0 0;
  user-select: none;
  border-bottom: 2px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  transition: all 0.2s ease;
}

.date-group-header-content {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
}

.date-text {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-weight: 500;
}

.date-group-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1;
}

.date-delete-all-btn {
  background-color: transparent;
  color: #dc3545;
  border: none;
  font-size: 0.85rem;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.date-delete-all-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
  opacity: 1;
}

.date-group {
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0,0,0,0.08);
}

/* Área expandida de drag'n'drop */
.drag-drop-area {
  background: transparent;
  min-height: 300px;
  transition: all 0.3s ease;
  position: relative;
  padding: 0;
  margin: 0 auto;
  max-width: 1400px;
  border: 2px dashed transparent;
  border-radius: 12px;
}

.drag-drop-area.drag-over {
  border-color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.03);
}

.image-group-container {
  background: transparent;
  min-height: 400px;
  max-height: calc(100vh - 300px);
  overflow-y: auto;
  transition: all 0.3s ease;
  position: relative;
  padding: 0;
  margin: 0 auto;
  border: none;
  scrollbar-width: thin;
  scrollbar-color: #dee2e6 transparent;
}

.image-group-container::-webkit-scrollbar {
  width: 8px;
}

.image-group-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

.image-group-container::-webkit-scrollbar-thumb {
  background-color: #dee2e6;
  border-radius: 10px;
  border: 2px solid transparent;
}

/* Classe drag-over movida para .drag-drop-area */

@media (max-width: 576px) {
  .image-group-container {
    min-height: 300px;
    max-height: calc(100vh - 200px);
  }
}

@media (min-width: 1600px) {
  .image-group-container {
    max-width: 1200px;
  }
}

.date-how-much {
  font-weight: 600;
  color: #495057;
  font-size: 0.85rem;
  background-color: rgba(13, 110, 253, 0.1);
  padding: 4px 10px;
  border-radius: 4px;
  margin-right: 8px;
  transition: all 0.2s ease;
  border: 1px solid rgba(13, 110, 253, 0.15);
  z-index: 1;
}

.no-images-message {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  min-height: 300px;
  text-align: center;
  color: #0d6efd;
}

.message-content {
  max-width: 500px;
  padding: 2.5rem;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  text-align: center;
  transform: translateY(-10px);
  animation: float 3s ease-in-out infinite;
}

.message-content .main-icon {
  color: #0d6efd;
  opacity: 0.8;
}

.message-content .main-text {
  font-size: 1.1rem;
  color: #495057;
  line-height: 1.5;
}

.drag-drop-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  font-size: 0.85rem;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(5px);
}

.drag-option, .click-option {
  display: flex;
  align-items: center;
  color: #6c757d;
  font-weight: 500;
  white-space: nowrap;
}

.divider-text {
  color: #676d74;
  font-size: 0.8rem;
  font-weight: 400;
}

@keyframes float {
  0% {
    transform: translateY(-5px);
  }
  50% {
    transform: translateY(5px);
  }
  100% {
    transform: translateY(-5px);
  }
}

/* Estilos para o header com botão e texto informativo */
.images-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.header-actions {
  display: flex;
  justify-content: flex-end;
}

.header-actions .btn {
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2);
  transition: all 0.2s ease;
}

.header-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  transition: all 0.3s ease;
}

.btn-fab i {
  font-size: 1.2rem;
}

.btn-fab:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

/* Removido o estilo de tab azul para usar o moving-tab */

.modelos-loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 30px;
}

.modelos-loading-spinner {
  margin-bottom: 20px;
}

.modelos-loading-text {
  font-size: 1rem;
  color: #6c757d;
  font-weight: 500;
}

/* Modo de diagnóstico compacto */
.diagnostic-mode {
  padding: 0 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 15px;
  margin: 8px 0;
}

.diagnostic-viewer-container {
  position: relative;
  padding: 15px 0;
}

.info-message-floating {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 16px;
  padding: 20px 24px;
  color: white;
  font-size: 0.95rem;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  animation: float-in 0.6s ease-out;
  backdrop-filter: blur(10px);
}

.info-message-floating:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
}

@keyframes float-in {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.close-info-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  margin-left: 15px;
  padding: 0;
  backdrop-filter: blur(10px);
}

.close-info-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.info-icon {
  color: white;
  font-size: 1rem;
  min-width: 18px;
}

/* Headers compactos dos grupos */
.diagnostic-group-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 12px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(13, 110, 253, 0.1);
  position: relative;
  overflow: hidden;
}

.diagnostic-group-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #0d6efd, #0a58ca, #0d6efd);
  background-size: 200% 100%;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.group-icon-wrapper {
  margin: 0;
}

.group-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  box-shadow: 0 4px 15px rgba(13, 110, 253, 0.25);
  transition: all 0.3s ease;
}

.group-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(13, 110, 253, 0.35);
}

.diagnostic-group-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #0d6efd;
  margin: 0;
  text-align: center;
  letter-spacing: 0.3px;
}

.group-description {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 400;
  opacity: 0.8;
  margin: 0;
}

/* Containers compactos dos slots */
.diagnostic-slots-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
  padding: 10px;
}

.extra-bucais-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
  max-width: 800px;
  margin: 0 auto;
}

.radiografias-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  max-width: 500px;
  margin: 0 auto;
}

/* Layout específico para imagens intra-bucais */
.diagnostic-slots-container.intra-bucais-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  width: 100%;
}

/* Primeira linha - 3 imagens */
.intra-bucais-layout .intra-bucais-row-1 {
  display: flex;
  justify-content: center;
  gap: 15px;
  width: 100%;
}

/* Segunda linha - 2 imagens centralizadas */
.intra-bucais-layout .intra-bucais-row-2 {
  display: flex;
  justify-content: center;
  gap: 15px;
  width: 100%;
}

/* Em telas pequenas, volta ao layout flexível normal */
@media (max-width: 768px) {
  .diagnostic-slots-container.intra-bucais-layout {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }

  .intra-bucais-layout .intra-bucais-row-1,
  .intra-bucais-layout .intra-bucais-row-2 {
    display: contents;
  }
}

/* Slots compactos e modernos */
.diagnostic-slot.modern-slot {
  width: 100%;
  min-width: 180px;
  border: none;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
  backdrop-filter: blur(10px);
  border: 2px solid transparent;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  will-change: transform, box-shadow;
}

.diagnostic-slot.modern-slot:not(.has-image) {
  height: 140px;
  justify-content: center;
  background: linear-gradient(135deg, rgba(13, 110, 253, 0.03) 0%, rgba(10, 88, 202, 0.03) 100%);
  border: 2px dashed rgba(13, 110, 253, 0.25);
}

.diagnostic-slot.modern-slot:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow: 0 12px 30px rgba(13, 110, 253, 0.12);
  border-color: rgba(13, 110, 253, 0.4);
}

.diagnostic-slot.modern-slot.slot-active {
  border-color: #0d6efd;
  background: linear-gradient(135deg, rgba(13, 110, 253, 0.08) 0%, rgba(10, 88, 202, 0.08) 100%);
  transform: translateY(-2px);
}

.diagnostic-slot.modern-slot.has-image {
  border: 2px solid rgba(13, 110, 253, 0.15);
  background: white;
  transform: translateY(-2px);
}

.diagnostic-slot.modern-slot.has-image:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 15px 35px rgba(13, 110, 253, 0.15);
  border-color: #0d6efd;
}

/* Slots específicos para intra-bucais */
.diagnostic-slot.intra-slot {
  min-width: 160px;
}

/* Slots específicos para radiografias */
.diagnostic-slot.radio-slot {
  min-width: 180px;
}

/* Estados vazios compactos */
.slot-empty.modern-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #0d6efd;
  height: 100%;
  padding: 15px;
}

.empty-icon-wrapper {
  position: relative;
  margin-bottom: 10px;
}

.slot-icon {
  font-size: 2rem;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.upload-indicator {
  position: absolute;
  bottom: -3px;
  right: -3px;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #0d6efd, #0a58ca);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.7rem;
  box-shadow: 0 3px 10px rgba(13, 110, 253, 0.25);
  animation: pulse-indicator 2s infinite;
}

@keyframes pulse-indicator {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.plus-icon {
  font-size: 0.6rem;
}

.slot-label {
  font-size: 0.9rem;
  text-align: center;
  font-weight: 600;
  margin-bottom: 3px;
  color: #2d3748;
}

.slot-hint {
  font-size: 0.7rem;
  color: #6c757d;
  opacity: 0.7;
  font-weight: 400;
}

.diagnostic-slot.modern-slot:hover .slot-icon {
  opacity: 0.8;
  transform: scale(1.05);
}

.diagnostic-slot.modern-slot:hover .upload-indicator {
  animation: pulse-indicator 1s infinite;
}

/* Preview compacto das imagens */
.slot-preview.modern-preview {
  width: 100%;
  height: 120px;
  position: relative;
  overflow: hidden;
  border-radius: 10px 10px 0 0;
  margin-bottom: 0;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.slot-preview.modern-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  filter: brightness(0.96) contrast(1.03);
}

.slot-preview.modern-preview:hover img {
  transform: scale(1.05);
  filter: brightness(1) contrast(1.05);
}

/* Botão de editar no canto superior esquerdo */
.slot-edit-btn-modern {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: rgba(13, 110, 253, 0.9);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 3px 12px rgba(13, 110, 253, 0.3);
  opacity: 0;
  transform: scale(0.8);
  z-index: 10;
}

.diagnostic-slot.modern-slot:hover .slot-edit-btn-modern {
  opacity: 1;
  transform: scale(1);
}

.slot-edit-btn-modern:hover {
  background: #0a58ca;
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(13, 110, 253, 0.4);
}

/* Labels compactos */
.slot-label-card.modern-label {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
  color: #2d3748;
  padding: 8px 6px;
  font-size: 0.8rem;
  font-weight: 600;
  text-align: center;
  letter-spacing: 0.2px;
  border-radius: 0 0 10px 10px;
  box-shadow: 0 -1px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-top: 1px solid rgba(13, 110, 253, 0.08);
  margin-top: 0;
  backdrop-filter: blur(10px);
}

.diagnostic-slot.modern-slot:hover .slot-label-card.modern-label {
  background: linear-gradient(135deg, rgba(13, 110, 253, 0.08), rgba(10, 88, 202, 0.08));
  color: #0d6efd;
  box-shadow: 0 -2px 12px rgba(13, 110, 253, 0.12);
  border-top-color: rgba(13, 110, 253, 0.2);
}

/* Responsividade compacta para dispositivos móveis */
@media (max-width: 768px) {
  .diagnostic-mode {
    padding: 0 10px;
  }

  .diagnostic-group-header {
    padding: 10px 15px;
    gap: 8px;
  }

  .group-icon {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .diagnostic-group-title {
    font-size: 1rem;
  }

  .group-description {
    font-size: 0.7rem;
  }

  .diagnostic-slots-container {
    padding: 8px;
    gap: 12px;
  }

  .extra-bucais-grid,
  .radiografias-grid {
    grid-template-columns: 1fr;
    max-width: 100%;
    gap: 12px;
  }

  .diagnostic-slot.modern-slot {
    min-width: unset;
    width: 100%;
  }

  .diagnostic-slot.modern-slot:not(.has-image) {
    height: 120px;
  }

  .slot-preview.modern-preview {
    height: 100px;
  }

  .slot-edit-btn-modern {
    opacity: 1;
    transform: scale(1);
    width: 28px;
    height: 28px;
    font-size: 0.7rem;
  }
}

/* Efeitos especiais e animações */
.diagnostic-group {
  animation: fade-in-up 0.6s ease-out;
  animation-fill-mode: both;
}

.diagnostic-group:nth-child(1) { animation-delay: 0.1s; }
.diagnostic-group:nth-child(2) { animation-delay: 0.2s; }
.diagnostic-group:nth-child(3) { animation-delay: 0.3s; }

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Efeito de brilho sutil nos slots */
.diagnostic-slot.modern-slot::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
  z-index: 1;
  pointer-events: none;
}

.diagnostic-slot.modern-slot:hover::before {
  left: 100%;
}

/* Efeito de partículas flutuantes */
.diagnostic-mode::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(240, 147, 251, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
  animation: float-particles 20s ease-in-out infinite;
}

@keyframes float-particles {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

/* Efeito de hover global no container */
.diagnostic-viewer-container:hover .diagnostic-slot.modern-slot:not(:hover) {
  opacity: 0.7;
  transform: scale(0.98);
}

/* Transição suave para todos os elementos */
* {
  transition: opacity 0.3s ease, transform 0.3s ease;
}


</style>

<script>
import { uploadImage, excluirImagem, excluirImagensPorData } from "@/services/imagensService";
import cSwal from "@/utils/cSwal.js";
import { getImageDescription } from "@/helpers/utils";
import { ref } from "vue";
import Modelos3D from "./Modelos3D.vue";
import setNavPills from "@/assets/js/nav-pills.js";

export default {
  name: "FotosRadiografias",
  components: {
    Modelos3D
  },
  props: {
    paciente: {
      type: Object,
      default: () => ({ imagens: [] }),
    },
    hideAnalyseButton: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'regular', // 'regular' or 'diagnostic'
      validator: (value) => ['regular', 'diagnostic'].includes(value)
    }
  },
  emits: ["selectTab", "pacienteChange"],
  setup() {
    const activeTab = ref("fotosRadiografias");
    const modelosLoaded = ref(false);
    return { activeTab, modelosLoaded };
  },
  data() {
    return {
      pendingImageFiles: [],
      pendingImagePreviews: [],
      pendingImageMetadata: [], // New array to hold date and description for each pending image
      dragOverContainer: false, // Track drag over state for container
      modelosLoadingTimeout: null,
      showDiagnosticInfo: true, // Controla a exibição do balão de informações
      // Slots para imagens de diagnóstico
      diagnosticSlots: {
        extraBucais: [
          { id: 'frente', label: 'Frente', tag: 'extra_frente', file: null, preview: null, imageRef: null },
          { id: 'frente_sorriso', label: 'Frente c/ sorriso', tag: 'extra_frente_sorriso', file: null, preview: null, imageRef: null },
          { id: 'perfil', label: 'Perfil', tag: 'extra_perfil', file: null, preview: null, imageRef: null },
          { id: 'perfil_sorriso', label: 'Perfil c/ sorriso', tag: 'extra_perfil_sorriso', file: null, preview: null, imageRef: null },
        ],
        intraBucais: [
          { id: 'frontal', label: 'Frontal', tag: 'intra_frontal', file: null, preview: null, imageRef: null },
          { id: 'oclusal_superior', label: 'Oclusal superior', tag: 'intra_oclusal_superior', file: null, preview: null, imageRef: null },
          { id: 'oclusal_inferior', label: 'Oclusal inferior', tag: 'intra_oclusal_inferior', file: null, preview: null, imageRef: null },
          { id: 'lateral_direita', label: 'Lateral direita', tag: 'intra_lateral_direita', file: null, preview: null, imageRef: null },
          { id: 'lateral_esquerda', label: 'Lateral esquerda', tag: 'intra_lateral_esquerda', file: null, preview: null, imageRef: null },
        ],
        radiografias: [
          { id: 'panoramica', label: 'Panorâmica', tag: 'radio_panoramica', file: null, preview: null, imageRef: null },
          { id: 'telerradiografia', label: 'Telerradiografia', tag: 'radio_telerradiografia', file: null, preview: null, imageRef: null },
        ]
      },
      // Slot ativo para upload
      activeSlot: null,
    };
  },
  computed: {
    safePatientImages() {
      const images = Array.isArray(this.paciente?.imagens) ? this.paciente.imagens : [];
      console.log('safePatientImages computado:', images.length, 'imagens');
      return images;
    },
    // Filtra imagens de diagnóstico (is_diagnostico = true ou is_diagnostico = 1)
    diagnosticImages() {
      const diagImages = this.safePatientImages.filter(img => {
        // Verifica se is_diagnostico é true ou 1 (pode vir como número do backend)
        const isDiagnostic = img.is_diagnostico === true || img.is_diagnostico === 1;
        console.log(`Imagem ${img.id} - is_diagnostico: ${img.is_diagnostico} (${typeof img.is_diagnostico}) - É diagnóstico: ${isDiagnostic}`);
        return isDiagnostic;
      });

      console.log('diagnosticImages computado:', diagImages.length, 'imagens de diagnóstico');
      console.log('Tags de diagnóstico disponíveis:', diagImages.map(img => img.tag_diagnostico));

      // Verificação específica para extra_frente_sorriso
      const frenteSorrisoImg = diagImages.find(img => img.tag_diagnostico === 'extra_frente_sorriso');
      if (frenteSorrisoImg) {
        console.log('Imagem extra_frente_sorriso encontrada no computed:', frenteSorrisoImg.id);
      } else {
        console.log('Imagem extra_frente_sorriso NÃO encontrada no computed');
      }

      return diagImages;
    },
    // Filtra imagens regulares (is_diagnostico = false ou 0 ou undefined)
    regularImages() {
      return this.safePatientImages.filter(img => {
        // Verifica se is_diagnostico não é true nem 1
        return img.is_diagnostico !== true && img.is_diagnostico !== 1;
      });
    },
    // Agrupa imagens por data, usando as imagens regulares (não diagnósticas)
    groupedImagesByDate() {
      const groups = {};
      this.regularImages.forEach((img) => {
        const date = img.data || "Sem data";
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(img);
      });
      // Convert to array of { date, images } sorted by date descending
      return Object.keys(groups)
        .map((date) => ({ date, images: groups[date] }))
        .sort((a, b) => (a.date < b.date ? 1 : -1));
    },
    // Agrupa imagens de diagnóstico por tag
    groupedDiagnosticImages() {
      const result = {
        extraBucais: [],
        intraBucais: [],
        radiografias: []
      };

      this.diagnosticImages.forEach(img => {
        const tag = img.tag_diagnostico || '';

        if (tag.startsWith('extra_')) {
          result.extraBucais.push(img);
        } else if (tag.startsWith('intra_')) {
          result.intraBucais.push(img);
        } else if (tag.startsWith('radio_')) {
          result.radiografias.push(img);
        }
      });

      return result;
    },
    // Verifica se deve mostrar o balão de informações de diagnóstico
    shouldShowDiagnosticInfo() {
      // Mostrar apenas se não houver imagens de diagnóstico e o usuário não fechou o balão
      return this.diagnosticImages.length === 0 && this.showDiagnosticInfo;
    },
    // Primeira linha das imagens intra-bucais
    intraBucaisRow1() {
      const slots = this.diagnosticSlots.intraBucais;
      const row1Order = ['lateral_direita', 'frontal', 'lateral_esquerda'];

      return row1Order.map(id => slots.find(slot => slot.id === id)).filter(Boolean);
    },
    // Segunda linha das imagens intra-bucais
    intraBucaisRow2() {
      const slots = this.diagnosticSlots.intraBucais;
      const row2Order = ['oclusal_superior', 'oclusal_inferior'];

      return row2Order.map(id => slots.find(slot => slot.id === id)).filter(Boolean);
    },
  },
  methods: {
    getImageDescription,

    // Método para fechar o balão de informações de diagnóstico
    closeDiagnosticInfo() {
      this.showDiagnosticInfo = false;
    },

    dateDmy(dateStr) {
      if (!dateStr || dateStr === "Sem data") return dateStr;
      const d = new Date(dateStr);
      if (isNaN(d)) return dateStr;
      const day = String(d.getDate()).padStart(2, "0");
      const month = String(d.getMonth() + 1).padStart(2, "0");
      const year = d.getFullYear();
      return `${day}/${month}/${year}`;
    },

    iniciarAnalise() {
      this.$emit("selectTab", "analise");
    },

    selectTab(tab) {
      this.activeTab = tab;

      // Carregar modelos 3D sob demanda quando a aba for selecionada
      if (tab === 'modelos3d' && !this.modelosLoaded) {
        // Limpar qualquer timeout anterior
        if (this.modelosLoadingTimeout) {
          clearTimeout(this.modelosLoadingTimeout);
        }

        // Simular carregamento com um pequeno delay para mostrar o spinner
        this.modelosLoadingTimeout = setTimeout(() => {
          this.modelosLoaded = true;
        }, 800); // Delay para mostrar o spinner
      }
    },

    chooseImageFile(inputId = "imageFileInput") {
      document.getElementById(inputId)?.click();
    },

    openModeloFileInput() {
      // Delega para o componente filho
      this.$nextTick(() => {
        document.getElementById("modeloFileInput")?.click();
      });
    },

    setImagePreviews(event) {
      const files = Array.from(event.target.files);

      // Append new files to existing ones instead of replacing
      this.pendingImageFiles = [...this.pendingImageFiles, ...files];

      // Process each new file
      files.forEach((file) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          // Append to existing previews instead of replacing
          this.pendingImagePreviews.push(e.target.result);
          // Initialize metadata with current date and empty description
          this.pendingImageMetadata.push({
            date: new Date().toISOString().slice(0, 10),
            description: "",
          });
        };
        reader.readAsDataURL(file);
      });

      // Reset the file input to allow selecting the same files again
      event.target.value = "";
    },

    removeImagePreview(index) {
      // Remove the image at the specified index from all related arrays
      this.pendingImageFiles.splice(index, 1);
      this.pendingImagePreviews.splice(index, 1);
      this.pendingImageMetadata.splice(index, 1);

      // If all images are removed, reset the file input
      if (this.pendingImageFiles.length === 0) {
        const fileInput = document.getElementById("imageFileInput");
        if (fileInput) fileInput.value = "";
      }
    },

    cancelImageUpload() {
      this.pendingImageFiles = [];
      this.pendingImagePreviews = [];
      this.pendingImageMetadata = []; // Clear metadata as well
      const fileInput = document.getElementById("imageFileInput");
      if (fileInput) fileInput.value = "";
    },

    async confirmImageUpload() {
      if (this.pendingImageFiles.length === 0) {
        cSwal.cError("Nenhum arquivo selecionado.");
        return;
      }

      cSwal.loading("Adicionando imagens...");

      try {
        for (let i = 0; i < this.pendingImageFiles.length; i++) {
          const file = this.pendingImageFiles[i];
          const imgData = {
            paciente_id: this.paciente.id,
            imagem: file,
            dir: "image",
            data: this.pendingImageMetadata[i]?.date || new Date().toISOString().slice(0, 10),
            descricao: this.pendingImageMetadata[i]?.description || "",
            etapa: "",
            is_diagnostico: false, // Imagens regulares sempre com is_diagnostico = false
          };
          await uploadImage(imgData);
        }
        cSwal.loaded();
        cSwal.cSuccess("Imagens adicionadas com sucesso.");
        this.cancelImageUpload();
        this.$emit("pacienteChange");
      } catch (error) {
        cSwal.loaded();
        console.error("Erro no upload de imagens:", error);
        cSwal.cError("Erro inesperado ao adicionar as imagens.");
      }
    },

    onDragOverContainer() {
      this.dragOverContainer = true;
    },

    onDragLeaveContainer() {
      this.dragOverContainer = false;
    },

    onDropContainer(event) {
      this.dragOverContainer = false;
      const dt = event.dataTransfer;
      if (!dt) return;
      const files = Array.from(dt.files).filter(file => file.type.startsWith('image/'));
      if (files.length === 0) return;

      // Simulate input#imageFileInput change event with these files
      const dataTransfer = new DataTransfer();
      files.forEach(file => dataTransfer.items.add(file));
      const fileInput = document.getElementById('imageFileInput');
      if (fileInput) {
        fileInput.files = dataTransfer.files;
        // Trigger change event manually
        const event = new Event('change', { bubbles: true });
        fileInput.dispatchEvent(event);
      }
    },

    confirmarExcluirImagem(image) {
      if (!image || !image.id) {
        cSwal.cError("Não foi possível identificar a imagem para exclusão.");
        return;
      }

      // Usando o método cConfirm do cSwal com o formato correto
      cSwal.cConfirm(
        "Tem certeza que deseja excluir esta imagem?<br><br>Esta ação não pode ser desfeita.",
        () => this.excluirImagem(image.id),
        {
          title: "Excluir imagem",
          icon: "warning",
          confirmButtonText: "Sim, excluir",
          cancelButtonText: "Cancelar",
        }
      );
    },

    async excluirImagem(id) {
      cSwal.loading("Excluindo imagem...");

      try {
        const resultado = await excluirImagem(id);

        if (resultado) {
          cSwal.loaded();
          cSwal.cSuccess("Imagem excluída com sucesso.");
          this.$emit("pacienteChange"); // Atualizar a lista de imagens
        } else {
          cSwal.loaded();
          cSwal.cError("Não foi possível excluir a imagem. Tente novamente.");
        }
      } catch (error) {
        console.error("Erro ao excluir imagem:", error);
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao excluir a imagem.");
      }
    },

    confirmarExcluirTodasImagens(data) {
      if (!data) {
        cSwal.cError("Data inválida para exclusão de imagens.");
        return;
      }

      // Formatando a data para exibição
      const dataFormatada = this.$filters.dateLong(data);

      // Usando o método cConfirm do cSwal com o formato correto
      cSwal.cConfirm(
        `Tem certeza que deseja excluir todas as imagens de <b>${dataFormatada}</b>?<br><br>Esta ação não pode ser desfeita.`,
        () => this.excluirTodasImagensPorData(data),
        {
          title: "Excluir todas as imagens",
          icon: "warning",
          confirmButtonText: "Sim, excluir todas",
          cancelButtonText: "Cancelar",
        }
      );
    },

    async excluirTodasImagensPorData(data) {
      if (!this.paciente || !this.paciente.id) {
        cSwal.cError("Paciente não identificado.");
        return;
      }

      cSwal.loading("Excluindo imagens...");

      try {
        const resultado = await excluirImagensPorData(this.paciente.id, data);

        if (resultado) {
          cSwal.loaded();
          cSwal.cSuccess("Todas as imagens foram excluídas com sucesso.");
          this.$emit("pacienteChange"); // Atualizar a lista de imagens
        } else {
          cSwal.loaded();
          cSwal.cError("Não foi possível excluir as imagens. Tente novamente.");
        }
      } catch (error) {
        console.error("Erro ao excluir imagens por data:", error);
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao excluir as imagens.");
      }
    },

    // Métodos para o modo de diagnóstico
    handleSlotClick(slot) {
      // Se o slot não tem imagem, abre o upload
      if (!slot.preview) {
        this.selectDiagnosticSlot(slot);
      }
      // Se tem imagem, não faz nada (deixa o v-viewer funcionar)
    },

    selectDiagnosticSlot(slot) {
      console.log('Slot selecionado:', slot);

      // Armazena o slot ativo para o upload
      this.activeSlot = slot;

      // Abre o seletor de arquivo
      const fileInput = document.getElementById('diagnosticImageInput');
      if (fileInput) {
        console.log('Abrindo seletor de arquivo');
        fileInput.click();
      } else {
        console.error('Elemento diagnosticImageInput não encontrado');
      }
    },

    editDiagnosticSlot(slot) {
      console.log('Editando slot:', slot);
      this.selectDiagnosticSlot(slot);
    },

    onDragOverSlot(slot) {
      // Destaca o slot quando um arquivo está sendo arrastado sobre ele
      this.activeSlot = slot;
    },

    onDragLeaveSlot() {
      // Remove o destaque quando o arquivo sai da área do slot
      this.activeSlot = null;
    },

    onDropSlot(event, slot) {
      // Processa o arquivo quando é solto no slot
      const dt = event.dataTransfer;
      if (!dt) return;

      const files = Array.from(dt.files).filter(file => file.type.startsWith('image/'));
      if (files.length === 0) return;

      // Usa apenas o primeiro arquivo
      const file = files[0];
      this.activeSlot = slot;

      // Se o slot já tem uma imagem, confirma a substituição
      if (slot.preview) {
        cSwal.cConfirm(
          `Já existe uma imagem no slot <b>${slot.label}</b>.<br><br>Deseja substituí-la?`,
          () => this.processDiagnosticImage(file),
          {
            title: "Substituir imagem",
            icon: "question",
            confirmButtonText: "Sim, substituir",
            cancelButtonText: "Cancelar",
          }
        );
      } else {
        // Se não tem imagem, processa diretamente
        this.processDiagnosticImage(file);
      }
    },

    handleDiagnosticImageSelect(event) {
      console.log('Imagem selecionada para diagnóstico:', event.target.files);

      // Processa a imagem selecionada para o slot ativo
      if (!this.activeSlot) {
        console.error('Nenhum slot ativo para receber a imagem');
        return;
      }

      if (!event.target.files.length) {
        console.error('Nenhum arquivo selecionado');
        return;
      }

      const file = event.target.files[0];
      console.log('Processando arquivo:', file);

      // Se o slot já tem uma imagem, confirma a substituição
      if (this.activeSlot.preview) {
        cSwal.cConfirm(
          `Já existe uma imagem no slot <b>${this.activeSlot.label}</b>.<br><br>Deseja substituí-la?`,
          () => this.processDiagnosticImage(file),
          {
            title: "Substituir imagem",
            icon: "question",
            confirmButtonText: "Sim, substituir",
            cancelButtonText: "Cancelar",
          }
        );
      } else {
        // Se não tem imagem, processa diretamente
        this.processDiagnosticImage(file);
      }

      // Limpa o input para permitir selecionar o mesmo arquivo novamente
      event.target.value = '';
    },

    processDiagnosticImage(file) {
      console.log('Processando imagem para diagnóstico:', file);

      // Verifica se há um slot ativo
      if (!this.activeSlot) {
        console.error('Nenhum slot ativo para processar a imagem');
        return;
      }

      console.log('Slot ativo:', this.activeSlot);

      // Lê o arquivo e gera o preview
      const reader = new FileReader();
      reader.onload = (e) => {
        console.log('Arquivo lido com sucesso, atualizando preview');

        // Atualiza o slot com o arquivo e o preview
        this.activeSlot.file = file;
        this.activeSlot.preview = e.target.result;

        // Faz o upload da imagem para o servidor
        this.uploadDiagnosticImage(this.activeSlot);
      };

      reader.onerror = (error) => {
        console.error('Erro ao ler o arquivo:', error);
        cSwal.cError('Erro ao processar a imagem. Por favor, tente novamente.');
      };

      reader.readAsDataURL(file);
    },

    async uploadDiagnosticImage(slot) {
      console.log('Iniciando upload de imagem para o slot:', slot);
      if (!slot || !slot.file || !this.paciente.id) {
        console.error('Dados inválidos para upload:', { slot, pacienteId: this.paciente.id });
        return;
      }

      // Verificar se há uma imagem existente para excluir
      const existingImage = this.diagnosticImages.find(img => img.tag_diagnostico === slot.tag);

      cSwal.loading(`${existingImage ? 'Substituindo' : 'Enviando'} imagem ${slot.label}...`);

      try {
        // Se há uma imagem existente, excluí-la primeiro
        if (existingImage) {
          console.log('Excluindo imagem existente:', existingImage.id);
          await excluirImagem(existingImage.id);
        }

        const imgData = {
          paciente_id: this.paciente.id,
          imagem: slot.file,
          dir: "image",
          data: new Date().toISOString().slice(0, 10),
          descricao: slot.label,
          etapa: "",
          is_diagnostico: true,
          tag_diagnostico: slot.tag
        };

        console.log('Dados para upload:', imgData);
        const response = await uploadImage(imgData);
        console.log('Resposta do upload:', response);

        cSwal.loaded();
        cSwal.cSuccess(`Imagem ${slot.label} ${existingImage ? 'substituída' : 'adicionada'} com sucesso.`);

        // Emitir evento para atualizar as imagens do paciente
        this.$emit("pacienteChange");

        // Forçar uma atualização dos slots após um pequeno atraso
        setTimeout(() => {
          console.log('Atualizando slots após upload bem-sucedido');
          this.populateDiagnosticSlots();
        }, 300);
      } catch (error) {
        cSwal.loaded();
        console.error("Erro no upload de imagem de diagnóstico:", error);
        cSwal.cError(`Erro ao ${existingImage ? 'substituir' : 'adicionar'} a imagem ${slot.label}.`);

        // Limpar o slot em caso de erro
        slot.file = null;
        slot.preview = null;
        slot.imageRef = null;
      }
    },

    removeDiagnosticImage(slot) {
      console.log('Removendo imagem do slot:', slot);

      // Encontra a imagem correspondente no array de imagens do paciente
      const image = this.diagnosticImages.find(img => img.tag_diagnostico === slot.tag);
      console.log('Imagem encontrada para exclusão:', image);

      if (image) {
        // Se a imagem existe no servidor, confirma a exclusão
        this.confirmarExcluirImagem(image);
      } else {
        // Se a imagem existe apenas localmente, limpa o slot
        console.log('Limpando slot local');
        slot.file = null;
        slot.preview = null;
        slot.imageRef = null;
      }
    },

    // Método para verificar diretamente o slot extra_frente_sorriso
    checkFrenteSorrisoSlot() {
      console.log('Verificando diretamente o slot extra_frente_sorriso');

      // Verificação direta no array de imagens do paciente
      const allImages = this.safePatientImages;
      console.log('Total de imagens do paciente:', allImages.length);

      // Procura diretamente por uma imagem com a tag extra_frente_sorriso
      const frente_sorriso_img = allImages.find(img => {
        const isMatch = img.tag_diagnostico === 'extra_frente_sorriso';
        console.log(`Imagem ${img.id} - tag: ${img.tag_diagnostico} - Match: ${isMatch}`);
        return isMatch;
      });

      if (frente_sorriso_img) {
        console.log('ENCONTRADA imagem extra_frente_sorriso diretamente no array:', frente_sorriso_img);

        // Encontra o slot correspondente
        const extraBucaisGroup = this.diagnosticSlots.extraBucais;
        const frenteSorrisoSlot = extraBucaisGroup.find(s => s.tag === 'extra_frente_sorriso');

        if (frenteSorrisoSlot) {
          // Atualiza diretamente o slot
          frenteSorrisoSlot.preview = frente_sorriso_img.url;
          frenteSorrisoSlot.imageRef = frente_sorriso_img;

          console.log('Slot extra_frente_sorriso atualizado diretamente:', frenteSorrisoSlot.preview);
        }
      } else {
        console.log('NÃO ENCONTRADA imagem extra_frente_sorriso diretamente no array');
      }
    },

    // Método para forçar a atualização dos slots
    forceUpdateSlots() {
      console.log('Forçando atualização dos slots');

      // Verificação específica para o slot extra_frente_sorriso
      const frente_sorriso_img = this.diagnosticImages.find(img => img.tag_diagnostico === 'extra_frente_sorriso');
      if (frente_sorriso_img) {
        console.log('Forçando atualização do slot extra_frente_sorriso');

        // Encontra o slot correspondente
        const extraBucaisGroup = this.diagnosticSlots.extraBucais;
        const frenteSorrisoSlot = extraBucaisGroup.find(s => s.tag === 'extra_frente_sorriso');

        if (frenteSorrisoSlot) {
          // Atualiza diretamente o slot
          frenteSorrisoSlot.preview = frente_sorriso_img.url;
          frenteSorrisoSlot.imageRef = frente_sorriso_img;

          console.log('Slot extra_frente_sorriso atualizado manualmente:', frenteSorrisoSlot.preview);
        }
      }
    },

    // Método para preencher os slots com imagens existentes
    populateDiagnosticSlots() {
      console.log('Executando populateDiagnosticSlots');
      console.log('Imagens de diagnóstico disponíveis:', JSON.stringify(this.diagnosticImages));

      try {
        // Verificação específica para o slot extra_frente_sorriso
        const frente_sorriso_img = this.diagnosticImages.find(img => img.tag_diagnostico === 'extra_frente_sorriso');
        console.log('Imagem extra_frente_sorriso encontrada?', frente_sorriso_img ? 'SIM' : 'NÃO');
        if (frente_sorriso_img) {
          console.log('Detalhes da imagem extra_frente_sorriso:', JSON.stringify(frente_sorriso_img));
        }

        // Percorre todos os grupos de slots
        for (const groupKey in this.diagnosticSlots) {
          const group = this.diagnosticSlots[groupKey];
          console.log(`Processando grupo: ${groupKey}, slots:`, JSON.stringify(group.map(s => s.tag)));

          // Para cada slot no grupo
          for (let i = 0; i < group.length; i++) {
            const slot = group[i];
            console.log(`Verificando slot: ${slot.id}, tag: ${slot.tag}`);

            // Procura uma imagem com a tag correspondente
            const matchingImage = this.diagnosticImages.find(img => {
              const matches = img.tag_diagnostico === slot.tag;
              console.log(`Comparando tag_diagnostico: "${img.tag_diagnostico}" com "${slot.tag}" - Match: ${matches}`);
              return matches;
            });

            if (matchingImage) {
              console.log(`Imagem encontrada para o slot ${slot.id}:`, matchingImage.url);
              // Atualiza o slot com a imagem encontrada
              slot.preview = matchingImage.url;
              slot.imageRef = matchingImage;

              // Verificação adicional para confirmar que o slot foi atualizado
              console.log(`Slot ${slot.id} após atualização:`, JSON.stringify({
                id: slot.id,
                tag: slot.tag,
                preview: slot.preview ? 'definido' : 'null'
              }));
            } else {
              console.log(`Nenhuma imagem encontrada para o slot ${slot.id}`);
              // Limpa o slot se não houver imagem correspondente
              slot.preview = null;
              slot.imageRef = null;
            }
          }
        }

        // Verificação final para o slot extra_frente_sorriso
        const extraBucaisGroup = this.diagnosticSlots.extraBucais;
        const frenteSorrisoSlot = extraBucaisGroup.find(s => s.tag === 'extra_frente_sorriso');
        console.log('Estado final do slot extra_frente_sorriso:', JSON.stringify({
          id: frenteSorrisoSlot.id,
          tag: frenteSorrisoSlot.tag,
          preview: frenteSorrisoSlot.preview ? 'definido' : 'null'
        }));
      } catch (error) {
        console.error('Erro ao preencher slots de diagnóstico:', error);
      }
    },
  },
  mounted() {
    console.log('Componente Imagens montado, modo:', this.mode);

    // Inicializar o componente com o moving-tab
    setTimeout(() => {
      setNavPills();
    }, 100);

    // Preencher os slots de diagnóstico com imagens existentes
    // Usamos setTimeout para garantir que o componente esteja totalmente montado
    setTimeout(() => {
      this.populateDiagnosticSlots();

      // Forçar uma atualização adicional após um pequeno atraso
      setTimeout(() => {
        this.forceUpdateSlots();
      }, 500);
    }, 200);
  },

  watch: {
    // Observar mudanças nas imagens do paciente para atualizar os slots
    'paciente.imagens': {
      handler() {
        console.log('Watcher detectou mudança nas imagens do paciente');
        // Usamos setTimeout para garantir que a atualização ocorra após o Vue ter processado as mudanças
        setTimeout(() => {
          this.populateDiagnosticSlots();

          // Forçar uma atualização adicional após um pequeno atraso
          setTimeout(() => {
            this.forceUpdateSlots();
          }, 200);
        }, 100);
      },
      deep: true,
      immediate: true
    },
    // Observar mudanças no modo para atualizar os slots quando mudar para modo diagnóstico
    mode: {
      handler(newMode) {
        console.log('Modo alterado para:', newMode);
        if (newMode === 'diagnostic') {
          // Usamos setTimeout para garantir que a atualização ocorra após o Vue ter processado as mudanças
          setTimeout(() => {
            this.populateDiagnosticSlots();

            // Forçar uma atualização adicional após um pequeno atraso
            setTimeout(() => {
              this.forceUpdateSlots();
            }, 200);
          }, 100);
        }
      },
      immediate: true
    }
  },
  beforeUnmount() {
    // Limpar timeout se existir
    if (this.modelosLoadingTimeout) {
      clearTimeout(this.modelosLoadingTimeout);
    }
  },
};
</script>
