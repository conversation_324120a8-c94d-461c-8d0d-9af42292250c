<?php
// App/Scopes/ClinicaScope.php
namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class ClinicaScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        if (auth()->check()) {
            $user = auth()->payload();

            // Só aplica o filtro se NÃO for admin
            if (!$user['system_admin']) {
                $builder->where($model->getTable() . '.clinica_id', $user['clinica']['id']);
            }
        }
    }
}
