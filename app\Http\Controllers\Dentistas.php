<?php

namespace App\Http\Controllers;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\Dentista;
use App\Models\User;
use App\Models\Clinica;
use App\Models\MatriculaCounter;
use App\Traits\LogsActionHistory;
use Illuminate\Support\Facades\Hash;
use Spatie\Activitylog\Models\Activity;

class Dentistas extends Controller
{
    use LogsActionHistory;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $dentistas = Dentista::all();
        return response()->json($dentistas);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return 'create()';
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $body = $request->all();

        $clinicaId = $body['clinica_id'];

        if ($clinicaId == 'add') {
            $clinica = new Clinica();
            $clinica->nome = $body['novaClinica'];

            // Gerar slug único baseado no nome
            $baseSlug = Str::slug(mb_strtolower(preg_replace('/[\s]/', '-', $clinica->nome)), '-');
            $clinica->slug = $this->generateUniqueSlug($baseSlug);

            $clinica->save();
            $clinicaId = $clinica->id;
        }

        $user = User::create([
            'name' => $body['nome'],
            'username' => $body['username'],
            'password' => $body['senha'],
            'clinica_id' => $clinicaId,
        ]);

        $dentista = new Dentista();
        $dentista->clinica_id = $clinicaId;
        $dentista->nome = $body['nome'];
        $dentista->observacoes = isset($body['observacoes']) ? $body['observacoes'] : '';
        $dentista->user_id = $user->id;

        $dentista->id_matricula = MatriculaCounter::getNextIdMatricula($dentista->clinica_id);

        $dentista->save();

        // Log the dentist creation action
        $this->logCreateAction($dentista, null, $request, "Created new dentist: {$dentista->nome}");

        // Log the user creation action
        $this->logCreateAction($user, null, $request, "Created user for dentist: {$dentista->nome}");

        return responseSuccess();
    }


    public function search(Request $request)
    {
        $body = $request->all();
        if (isset($body) && isset($body['search']))
            $search = $body['search'];
        else
            $search = '';

        $query = Dentista::with(['clinica', 'user'])
            ->withCount('pacientes');

        if (trim($search) !== '')
            $query->where('nome', 'like', '%' . $search . '%');

        $dentistas = $query->latest('created_at')
            ->get();

        return response()->json($dentistas);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // O $id_matricula recebido via parâmetro representa o ID da matrícula - cada clínica tem sua id_matricula incrementada independentemente
        $id_matricula = $id;

        $dentista = Dentista::with([
            'user',
            'clinica',
            'contatos',
        ])
            ->withCount(['pacientes', 'consultas'])
            ->where('id_matricula', $id_matricula)
            ->firstOrFail();

        if (!$dentista) {
            return response()->json(['error' => 'Dentista not found for this clinica.'], 404);
        }

        // Processar URL da imagem de perfil
        if ($dentista->profile_picture_url) {
            $dentista->profile_picture_url = imageUrl($dentista->profile_picture_url);
        }

        return response()->json($dentista);
    }

    /**
     * Display the specified resource with clinica_slug filtering.
     */
    public function showWithClinicaSlug(string $clinica_slug, string $id_matricula)
    {
        $user = auth()->payload();

        if (!$user['system_admin'] && $clinica_slug !== $user['clinica']['slug']) {
            return response()->json(['error' => 'Unauthorized. Admin access required.'], 403);
        }

        $clinica = Clinica::where('slug', $clinica_slug)->first();

        if (!$clinica) {
            return response()->json(['error' => 'Clinica not found.'], 404);
        }

        // Para admins, precisamos desabilitar o scope global temporariamente
        $query = $user['system_admin']
            ? Dentista::withoutGlobalScope(\App\Scopes\ClinicaScope::class)
            : Dentista::query();

        $dentista = $query->with([
            'user',
            'clinica',
            'contatos',
        ])
            ->where('clinica_id', $clinica->id)
            ->where('id_matricula', $id_matricula)
            ->first();

        if (!$dentista) {
            return response()->json(['error' => 'Dentista not found for this clinica.'], 404);
        }

        // Processar URL da imagem de perfil
        if ($dentista->profile_picture_url) {
            $dentista->profile_picture_url = imageUrl($dentista->profile_picture_url);
        }

        return response()->json($dentista);
    }

    public function getClinicaDentistas(string $clinica_slug) {
        $user = auth()->payload();

        $clinica = null;

        if (preg_match('/^\d+$/', $clinica_slug)) {
            // Find by ID in case $clinica_slug is just digits
            $clinica = Clinica::find($clinica_slug);

            if (!$clinica) {
                return response()->json(['error' => 'Clinica not found.'], 404);
            }

            $clinica_slug = $clinica->slug;
        }

        if (!$user['system_admin'] && $clinica_slug !== $user['clinica']['slug']) {
            return response()->json(['error' => 'Unauthorized. Admin access required.'], 403);
        }

        if (!$clinica) {
            $clinica = Clinica::where('slug', $clinica_slug)->first();
        }

        if (!$clinica) {
            return response()->json(['error' => 'Clinica not found.'], 404);
        }

        $dentistas = Dentista::with(['clinica', 'user'])
            ->withCount('pacientes')
            ->where('clinica_id', $clinica->id)
            ->latest('created_at')
            ->get();

        return response()->json($dentistas);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $dentista_id = $id;
            $existingDentista = Dentista::find($dentista_id);
            if (!$existingDentista)
                throw new Exception('Dentista não encontrado');

            // Capture original data before update
            $originalDentistaData = $this->captureOriginalData($existingDentista);
            $originalUserData = $this->captureOriginalData($existingDentista->user);

            $dentistaData = $request->all();

            // Atualizar o ID da clínica
            $existingDentista->clinica_id = $dentistaData['clinica']['id'];

            // Atualizar o e-mail do usuário
            $existingDentista->user->email = $dentistaData['user']['email'];

            // Atualizar o clinica_id do usuário para manter consistência
            $existingDentista->user->clinica_id = $dentistaData['clinica']['id'];

            // Atualizar a senha do usuário, se fornecida
            if (!empty($dentistaData['novaSenha'])) {
                $existingDentista->user->password = Hash::make($dentistaData['novaSenha']);
            }

            // Atualizar o modelo de dentista com os dados restantes
            $existingDentista->fill($dentistaData);

            // Salvar as alterações
            $existingDentista->save();

            // Salvar as alterações do usuário
            $existingDentista->user->save();

            // Log the dentist update action
            $this->logUpdateAction($existingDentista, $originalDentistaData, null, $request, "Updated dentist: {$existingDentista->nome}");

            // Log the user update action
            $this->logUpdateAction($existingDentista->user, $originalUserData, null, $request, "Updated user for dentist: {$existingDentista->nome}");

            $response = responseSuccess();
        } catch (\Exception $e) {
            // Log::error($e->getMessage());
            $response = responseError([
                'message' => $e->getMessage()
            ]);
        }

        return $response;
    }
    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $user = auth()->payload();

            // Find the dentist
            $dentista = Dentista::with('user')->find($id);

            // Check if dentist exists
            if (!$dentista) {
                return responseError(['message' => 'Ortodontista não encontrado']);
            }

            // Check permissions: only allow deletion if user is from the same clinic or is a system admin
            if (!$user['system_admin'] && $dentista->clinica_id != $user['clinica']['id']) {
                return responseError(['message' => 'Não autorizado. Você só pode excluir ortodontistas da sua própria clínica'], 403);
            }

            // Prevent self-deletion
            if ($dentista->user && $dentista->user->id == $user['id']) {
                return responseError(['message' => 'Você não pode excluir seu próprio perfil'], 403);
            }

            // Handle patients linked to this dentist
            $pacientesCount = $dentista->pacientes()->count();
            if ($pacientesCount > 0) {
                // Set dentista_id to null for all patients linked to this dentist
                $dentista->pacientes()->update(['dentista_id' => null]);
            }

            // Handle consultas linked to this dentist
            $consultasCount = $dentista->consultas()->count();
            if ($consultasCount > 0) {
                // Set dentista_id to null for all consultas linked to this dentist
                $dentista->consultas()->update(['dentista_id' => null]);
            }

            // Capture dentist data before deletion for logging
            $dentistaData = $dentista->toArray();

            // Delete contacts first (they will be deleted by CASCADE, but let's be explicit)
            $dentista->contatos()->delete();

            // Delete the associated user first
            if ($dentista->user) {
                $dentista->user->delete();
            }

            // Delete the dentist
            $dentista->delete();

            // Log the deletion action
            $this->logDeleteAction($dentista, $dentistaData, request(), "Deleted orthodontist: {$dentistaData['nome']}");

            return responseSuccess(['message' => 'Ortodontista excluído com sucesso']);

        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao excluir ortodontista: ' . $e->getMessage()
            ]);
        }
    }

    public function teste(Request $request)
    {
        $payload = auth()->payload();
        return $payload->get('customPayloadItem1');
    }

    /**
     * Gera um slug único adicionando números se necessário
     */
    private function generateUniqueSlug($baseSlug)
    {
        $slug = $baseSlug;
        $counter = 2;

        // Verifica se o slug já existe
        while (Clinica::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
